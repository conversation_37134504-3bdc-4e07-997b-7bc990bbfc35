"""
风险评估智能体模块，负责评估建筑安全隐患风险
基于CrewAI框架实现
"""

import os
import sys
import time
import json
import logging
import re
import gc
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
import psutil

from crewai import Agent, Task
from langchain_openai import ChatOpenAI

# 导入配置服务
from config.config import config_service
from agents.base_agent import BaseAgent, safe_execute
from models.constants import RiskLevel, get_risk_level, get_risk_level_text
from models.data_formats import HazardItem, RiskAssessment
from tools.graph_engine.core.engine import GraphEngine
from tools.law_rag_engine.rag_engine import LawRAGEngine
from utils.logger import get_logger
from utils.data_service import data_service
from utils.exceptions import TaskException, DataException
from utils.progress import create_progress_bar

from crewai import tools
from crewai.tools import BaseTool
from pydantic import BaseModel, Field

from utils.logger import LoggerMixin

# 导入工具模块 - 集中导入所有需要的工具
from tools.graph_engine import graph_tools_list, get_reasoning_context
# 导入GraphEngine类和工具函数
from tools.graph_engine.core.engine import GraphEngine

from .base_agent import BaseAgent

# 获取法规检索引擎实例
_law_rag_engine = LawRAGEngine.get_instance()

class EvaluatorAgent(BaseAgent):
    """
    风险评估智能体
    负责评估建筑安全隐患风险
    基于CrewAI框架实现
    """
    
    def __init__(
        self,
        name: str = "风险评估者",
        role: str = "建筑安全风险评估专家",
        goal: str = "评估建筑安全隐患的风险等级和法规符合性",
        backstory: Optional[str] = None,
        llm_config: Optional[Dict[str, Any]] = None,
        verbose: bool = False
    ):
        """初始化风险评估智能体"""
        backstory = backstory or "专业的建筑安全风险评估专家，负责评估安全隐患的风险等级和法规符合性。"

        # 创建法规检索工具
        search_law_tool = self._create_search_law_tool()

        super().__init__(
            name=name,
            role=role,
            goal=goal,
            backstory=backstory,
            llm_config=llm_config,
            verbose=verbose,
            tools=[search_law_tool]
        )
    
    def _create_search_law_tool(self):
        """创建法规搜索工具函数"""
        @tools.tool("搜索法律法规")
        def search_laws(query: str) -> str:
            """
            搜索与查询相关的法规条文
            
            Args:
                query: 查询文本，描述你想了解的建筑安全法规问题
                
            Returns:
                str: 相关法规条文及解释
            """
            global _law_rag_engine
            return _law_rag_engine.search_laws(query)
            
        return search_laws
    
    def get_law_rag_info(self, description: str) -> Tuple[Optional[str], bool, Optional[str]]:
        """
        获取法规信息

        Args:
            description: 隐患描述

        Returns:
            Tuple[Optional[str], bool, Optional[str]]: (法规条款, 是否强制性, 法规解释)
        """
        self.log_info("查询相关法规")

        try:
            # 直接使用法规检索引擎
            global _law_rag_engine
            law_result = _law_rag_engine.search_laws(description)

            if not law_result or law_result == "未找到相关法规":
                return None, False, None

            # 使用增强版法规解析
            law_info = self._parse_law_regulation_enhanced(law_result)

            return law_info["law_clause"], law_info.get("is_mandatory", False), law_info.get("law_explanation")

        except Exception as e:
            self.log_error(f"查询法规失败", e)
            return None, False, None

    def _parse_law_regulation_enhanced(self, law_result: str) -> Dict[str, Any]:
        """

        Args:
            law_result: 法规检索结果文本

        Returns:
            Dict[str, Any]: 解析后的法规信息
        """
        if not law_result or law_result == "未找到相关法规":
            return {
                "law_clause": None
            }

        # 不再进行硬编码的强制性判断，让LLM根据法规内容自行分析
        return {
            "law_clause": law_result
        }

    def extract_safety_keywords(self, description: str) -> List[str]:
        """
        使用LLM智能提取建筑安全隐患描述中的关键词

        Args:
            description: 隐患描述

        Returns:
            List[str]: 提取的关键词列表
        """
        return self._extract_keywords_with_llm(description)

    def _extract_keywords_with_llm(self, description: str) -> List[str]:
        """
        使用LLM提取关键词

        Args:
            description: 隐患描述

        Returns:
            List[str]: 提取的关键词列表
        """
        try:
            # 使用开放式LLM理解，无硬编码限制
            prompt = f"""请分析以下安全隐患描述，提取用于知识图谱查询的核心概念：

隐患描述：{description}

请从以下维度提取概念，每个维度1-2个最重要的概念：

1. 主要对象：涉及的设备、设施、构件、材料等具体实体
2. 问题性质：安全问题的类型、风险性质、故障模式等
3. 影响后果：可能导致的事故类型、危害结果等
4. 关键特征：描述中的重要技术特征、状态描述等

要求：
- 提取的概念应该是具体的、可查询的术语
- 避免过于抽象或宽泛的词汇
- 优先选择在工程安全领域有明确含义的专业术语
- 总共提取3-5个最核心的概念
- 用逗号分隔，不要其他解释

核心概念："""

            response = self.call_llm(prompt)
            if response:
                keywords = [k.strip() for k in response.split(',') if k.strip()]
                # 过滤掉过短或无效的关键词
                valid_keywords = [k for k in keywords if len(k) >= 2 and len(k) <= 10]

                # 使用LLM进行智能关键词优化
                optimized_keywords = self._optimize_keywords_with_llm(valid_keywords, description)
                return optimized_keywords[:4]  # 最多返回4个最相关的

        except Exception as e:
            self.log_warning(f"LLM关键词提取失败: {e}")

        return []

    def _optimize_keywords_with_llm(self, keywords: List[str], description: str) -> List[str]:
        """
        使用LLM智能优化关键词列表

        Args:
            keywords: 初始关键词列表
            description: 原始描述文本

        Returns:
            List[str]: 优化后的关键词列表
        """
        if not keywords:
            return []

        try:
            prompt = f"""请优化以下关键词列表，使其更适合知识图谱查询：

原始描述：{description}
初始关键词：{', '.join(keywords)}

请分析：
1. 这些关键词是否准确反映了描述的核心内容？
2. 是否有更精确或更通用的同义词？
3. 是否遗漏了重要的概念？
4. 是否有冗余或不相关的词汇？

优化要求：
- 保留最相关和最具查询价值的关键词
- 如果有更标准的专业术语，请替换
- 如果发现重要遗漏，请补充
- 最终返回3-4个最优关键词
- 用逗号分隔，不要其他解释

优化后的关键词："""

            result = self.call_llm(prompt)
            if result:
                optimized_keywords = [kw.strip() for kw in str(result).split(',') if kw.strip()]
                # 过滤掉过长或过短的关键词
                valid_keywords = [kw for kw in optimized_keywords if 2 <= len(kw) <= 15]
                self.log_info(f"关键词优化: {keywords} -> {valid_keywords}")
                return valid_keywords[:4]

        except Exception as e:
            self.log_warning(f"LLM关键词优化失败: {e}")

        # 如果LLM优化失败，返回原始关键词
        return keywords

    def _extract_core_description(self, description: str) -> str:
        """
        提取描述的核心部分用于语义查询

        Args:
            description: 原始描述

        Returns:
            str: 核心描述
        """
        # 移除常见的无关词汇
        stop_words = ['的', '了', '在', '是', '有', '和', '与', '或', '但', '而', '因为', '所以']

        # 提取关键句子（包含安全相关词汇的句子）
        safety_indicators = ['安全', '隐患', '风险', '危险', '事故', '倒塌', '坠落', '火灾', '漏电', '超载']

        sentences = description.replace('。', '|').replace('，', '|').split('|')
        core_sentences = []

        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 5:  # 过滤过短的句子
                # 检查是否包含安全相关词汇
                if any(indicator in sentence for indicator in safety_indicators):
                    core_sentences.append(sentence)

        if core_sentences:
            # 返回最长的核心句子（通常包含最多信息）
            return max(core_sentences, key=len)
        else:
            # 如果没有找到核心句子，返回原始描述
            return description

    def _select_best_graph_paths(self, causal_paths: List[Dict], description: str, query_terms: List[str]) -> str:
        """
        智能选择最相关的图谱路径（2-3条）

        Args:
            causal_paths: 因果路径列表
            description: 原始描述
            query_terms: 查询关键词

        Returns:
            str: 最佳图谱路径字符串（多条路径用换行分隔）
        """
        if not causal_paths:
            return ""

        # 使用LLM对路径进行智能评分
        scored_paths = []

        for path in causal_paths:
            path_nodes = path.get('nodes', [])

            if not path_nodes:
                continue

            # 检查原始相似度，过滤掉相似度过低的路径
            original_similarity = path.get('similarity', path.get('confidence', 0.0))
            # 从配置中获取最小相似度阈值
            from config.config import config_service
            kg_config = config_service.get_kg_config()
            min_similarity_threshold = kg_config.get('min_similarity', 0.7)

            if original_similarity < min_similarity_threshold:  # 硬性过滤相似度过低的路径
                self.log_info(f"过滤低相似度路径 (相似度: {original_similarity:.2f}): {' → '.join(path_nodes[:2])}")
                continue

            # 使用LLM评估路径相关性
            relevance_score = self._evaluate_path_relevance_with_llm(path_nodes, description, query_terms)

            # 路径完整性评分
            completeness_score = len(path_nodes) * 0.1

            # 路径置信度
            confidence = path.get('confidence', 0.5)

            # 综合评分
            total_score = relevance_score * 0.7 + completeness_score * 0.2 + confidence * 0.1

            scored_paths.append((path, total_score, path_nodes, relevance_score))

        if not scored_paths:
            return ""

        # 按得分排序
        scored_paths.sort(key=lambda x: x[1], reverse=True)

        # 获取质量阈值（现在统一要求0.8）
        from config.config import config_service
        kg_config = config_service.get_kg_config()
        min_quality_threshold = kg_config.get('high_quality_threshold', 0.8)

        # 严格过滤：只保留相关度≥0.8的路径
        qualified_paths = [p for p in scored_paths if p[3] >= min_quality_threshold]

        # 如果没有符合要求的路径，直接返回空字符串（将显示为NULL）
        if not qualified_paths:
            self.log_warning(f"所有图谱路径相关度低于{min_quality_threshold}，跳过图谱分析")
            return ""

        # 选择最多3条高质量路径（现在所有路径都保证包含案例节点）
        selected_paths = qualified_paths[:min(3, len(qualified_paths))]

        # 构建路径字符串，直接显示案例节点
        path_strings = []
        for i, (path, total_score, path_nodes, relevance_score) in enumerate(selected_paths, 1):
            if path_nodes:
                # 获取节点类型信息
                path_types = path.get('node_types', [])

                # 格式化路径节点，用【】标识案例节点
                formatted_nodes = []
                has_case = False

                for j, node in enumerate(path_nodes):
                    if j < len(path_types) and path_types[j] == 'case':
                        formatted_nodes.append(f"【{node}】")  # 案例节点用【】包围
                        has_case = True
                    else:
                        formatted_nodes.append(node)

                path_str = " → ".join(formatted_nodes)
                case_indicator = " (含历史案例)" if has_case else ""
                path_strings.append(f"{i}. {path_str}{case_indicator} (相关度: {relevance_score:.2f})")

        if path_strings:
            result = "\n".join(path_strings)
            case_count = sum(1 for path, _, _, _ in selected_paths
                           if any(j < len(path.get('node_types', [])) and path.get('node_types', [])[j] == 'case'
                                  for j in range(len(path.get('nodes', [])))))
            self.log_info(f"选择了 {len(path_strings)} 条最佳图谱路径，其中 {case_count} 条包含历史案例")
            return result

        return ""

    def _semantic_similarity(self, term1: str, term2: str) -> bool:
        """
        使用LLM检查两个术语的语义相似性

        Args:
            term1: 第一个术语
            term2: 第二个术语

        Returns:
            bool: 是否语义相似
        """
        try:
            prompt = f"""请判断以下两个术语在工程安全领域是否语义相关：

术语1: {term1}
术语2: {term2}

判断标准：
- 是否指向同一类设备、设施或概念？
- 是否在同一个安全风险场景中经常出现？
- 是否有因果关系或关联关系？

请只回答：相关 或 不相关"""

            result = self.call_llm(prompt)
            if result and "相关" in str(result) and "不相关" not in str(result):
                return True

        except Exception as e:
            self.log_warning(f"语义相似性检查失败: {e}")

        # LLM检查失败，返回False
        return False

    def _check_semantic_relevance(self, node: str, description: str) -> bool:
        """
        使用LLM检查节点与描述的语义相关性

        Args:
            node: 图谱节点名称
            description: 描述文本

        Returns:
            bool: 是否语义相关
        """
        try:
            prompt = f"""请判断以下图谱节点与安全隐患描述是否相关：

图谱节点: {node}
隐患描述: {description}

判断标准：
- 节点是否涉及描述中提到的设备、设施或问题？
- 节点是否与描述的安全风险场景相关？
- 节点是否可能是描述问题的原因或后果？

请只回答：相关 或 不相关"""

            result = self.call_llm(prompt)
            if result and "相关" in str(result) and "不相关" not in str(result):
                return True

        except Exception as e:
            self.log_warning(f"语义相关性检查失败: {e}")

        # LLM检查失败，返回False
        return False

    def _evaluate_path_relevance_with_llm(self, path_nodes: List[str], description: str, query_terms: List[str]) -> float:
        """
        使用LLM评估路径与描述的相关性

        Args:
            path_nodes: 路径节点列表
            description: 原始描述
            query_terms: 查询关键词

        Returns:
            float: 相关性评分 (0-1)
        """
        try:
            path_str = " → ".join(path_nodes)

            prompt = f"""请评估以下因果路径与安全隐患描述的相关性：

隐患描述: {description}
查询关键词: {', '.join(query_terms)}
因果路径: {path_str}

评估标准：
1. 路径是否涉及描述中的核心问题？
2. 路径的因果关系是否符合描述的情况？
3. 路径的结果是否与描述的风险一致？
4. 路径是否能解释或预测描述的安全隐患？

请给出0-10分的评分，并简要说明理由。
格式：评分: X分 理由: XXX"""

            result = self.call_llm(prompt)
            if result:
                # 提取评分
                result_str = str(result)
                if "评分:" in result_str:
                    score_part = result_str.split("评分:")[1].split("分")[0].strip()
                    try:
                        score = float(score_part) / 10.0  # 转换为0-1范围
                        self.log_info(f"LLM路径评分: {score:.2f} - {path_str}")
                        return max(0.0, min(1.0, score))
                    except ValueError:
                        pass

        except Exception as e:
            self.log_warning(f"LLM路径评估失败: {e}")

        # 如果LLM评估失败，使用简单的关键词匹配作为后备
        path_text = " ".join(path_nodes).lower()
        matches = sum(1 for term in query_terms if term.lower() in path_text)
        return matches / len(query_terms) if query_terms else 0.0

    def get_knowledge_graph_info(self, description: str) -> Tuple[Optional[Dict[str, Any]], Optional[str], Optional[str]]:
        """
        获取知识图谱信息

        从知识图谱中检索与隐患描述相关的信息，包括类似案例、因果路径等

        Args:
            description: 隐患描述文本

        Returns:
            Tuple[Optional[Dict[str, Any]], Optional[str], Optional[str]]:
                - 第一个元素: 知识图谱上下文数据，包含similar_cases(类似案例)、
                  causal_paths(因果路径)等信息；如果检索失败则为None
                - 第二个元素: 知识图谱分析的文本解释；如果检索失败则为None
                - 第三个元素: 图谱路径字符串；如果检索失败则为None

        Raises:
            Exception: 知识图谱查询过程中的任何异常都会被捕获并记录，不会向上传播
        """
        self.log_info("查询知识图谱")

        try:
            # 第一步：智能提取关键词
            keywords = self.extract_safety_keywords(description)
            self.log_info(f"提取的关键词: {keywords}")

            # 第二步：多策略图谱查询
            all_causal_paths = []
            successful_queries = 0

            # 如果没有提取到关键词，跳过图谱查询
            if not keywords:
                self.log_warning("未提取到关键词，跳过图谱查询")
                return None, None, None

            query_terms = keywords

            # 策略1：组合关键词查询
            try:
                self.log_info(f"策略1: 组合关键词查询，关键词: {query_terms}")

                # 使用组合关键词查询
                combined_query = ','.join(query_terms[:3])  # 最多使用前3个关键词
                result = get_reasoning_context([combined_query])

                if result and result.get('causal_paths'):
                    context_data = result
                    all_causal_paths.extend(context_data.get('causal_paths', []))
                    successful_queries += 1
                    self.log_info(f"组合查询成功: 找到 {len(context_data.get('causal_paths', []))} 条因果路径")

            except Exception as e:
                self.log_warning(f"组合关键词查询失败: {e}")

            # 策略2：单个关键词查询（如果组合查询结果不足）
            if len(all_causal_paths) < 2:
                self.log_info("策略2: 单个关键词查询")
                for query_term in query_terms[:3]:  # 最多查询前3个关键词
                    try:
                        self.log_info(f"使用单个关键词查询: {query_term}")
                        result = get_reasoning_context([query_term])

                        if result and result.get('causal_paths'):
                            context_data = result
                            new_paths = context_data.get('causal_paths', [])
                            # 避免重复路径
                            for path in new_paths:
                                if path not in all_causal_paths:
                                    all_causal_paths.append(path)
                            successful_queries += 1
                            self.log_info(f"单个关键词查询成功: 找到 {len(new_paths)} 条新路径")

                    except Exception as e:
                        self.log_warning(f"单个关键词查询失败 {query_term}: {e}")

            # 策略3：语义扩展查询（如果前两种策略结果仍不足）
            if len(all_causal_paths) < 1:
                self.log_info("策略3: 语义扩展查询")
                try:
                    # 使用描述的核心部分进行查询
                    core_description = self._extract_core_description(description)
                    if core_description:
                        result = get_reasoning_context([core_description])
                        if result and result.get('causal_paths'):
                            context_data = result
                            all_causal_paths.extend(context_data.get('causal_paths', []))
                            successful_queries += 1
                            self.log_info(f"语义扩展查询成功: 找到 {len(context_data.get('causal_paths', []))} 条路径")

                except Exception as e:
                    self.log_warning(f"语义扩展查询失败: {e}")

                # 如果语义查询失败，回退到逐个关键词查询
                self.log_info("回退到逐个关键词查询模式")
                for query_term in query_terms:
                    try:
                        self.log_info(f"使用关键词查询: {query_term}")
                        result = get_reasoning_context([query_term])

                        if result:
                            try:
                                # result 已经是字典，不需要JSON解析
                                context_data = result

                                if context_data:
                                    causal_paths = context_data.get('causal_paths', [])

                                    # 合并结果（避免重复）
                                    for path in causal_paths:
                                        if path not in all_causal_paths:
                                            all_causal_paths.append(path)

                                    successful_queries += 1

                            except Exception as e:
                                self.log_warning(f"处理关键词 '{query_term}' 的查询结果失败: {e}")
                                continue

                    except Exception as e:
                        self.log_warning(f"关键词 '{query_term}' 查询失败: {e}")
                        continue

            # 第三步：构建合并后的结果
            if successful_queries == 0:
                self.log_warning("所有关键词查询都失败")
                return None, None, None

            # 构建合并的上下文数据
            merged_context = {
                'causal_paths': all_causal_paths
            }

            explanation = f"根据知识图谱分析（使用{len(query_terms)}个关键词查询），发现{len(all_causal_paths)}条相关因果路径。"

            # 智能选择最相关的图谱路径（2-3条）
            graph_path = self._select_best_graph_paths(all_causal_paths, description, query_terms)

            return merged_context, explanation, graph_path

        except Exception as e:
            self.log_error(f"查询知识图谱失败", e)
            return None, None, None

    def _format_kg_info_for_llm(self, kg_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        将知识图谱信息格式化为LLM可理解的结构

        Args:
            kg_context: 原始知识图谱上下文数据

        Returns:
            Dict[str, Any]: 格式化后的知识图谱信息
        """
        if not kg_context:
            return {"context_data": None}

        similar_cases = kg_context.get('similar_cases', [])
        causal_paths = kg_context.get('causal_paths', [])

        # 简单格式化相似案例
        cases_summary = []
        for case in similar_cases:
            case_name = case.get('name', '未知案例')
            case_desc = case.get('description', '无描述')
            cases_summary.append(f"{case_name}: {case_desc}")

        # 简单格式化因果路径
        paths_summary = []
        for path in causal_paths:
            path_nodes = path.get('nodes', [])
            if path_nodes:
                path_desc = " → ".join(path_nodes)
                paths_summary.append(path_desc)

        return {
            "similar_cases_summary": cases_summary,
            "causal_paths_summary": "; ".join(paths_summary) if paths_summary else "无相关因果路径",
            "context_data": kg_context
        }

    def _build_enhanced_prompt(self, hazard: Dict[str, Any], law_clause: Optional[str],
                              kg_context: Optional[Dict[str, Any]],
                              image_description: str) -> str:
        """
        构建增强版评估提示词

        Args:
            hazard: 隐患信息
            law_clause: 法规条款
            kg_context: 知识图谱上下文
            image_description: 图像描述

        Returns:
            str: 完整的评估提示词
        """
        prompt = f"""你是资深建筑安全专家，请基于以下完整信息对建筑安全隐患进行专业评估：

【基本信息】
- 时间：{hazard.get('timestamp', '未知')}
- 地点：{hazard.get('location', '未知')}
- 描述：{hazard.get('hazard_description', '')}
- 来源：{hazard.get('author', '未知')}"""

        # 添加图像信息
        if image_description:
            prompt += f"\n- 现场图像：{image_description}"

        # 添加法规信息
        if law_clause:
            prompt += f"""

【法规依据】
- 相关条款：{law_clause}
- 请根据条款内容自行判断其强制性程度和违规后果严重性"""

        # 添加知识图谱信息
        if kg_context:
            # 格式化知识图谱信息
            formatted_kg = self._format_kg_info_for_llm(kg_context)
            prompt += f"""

【知识图谱分析】
- 历史案例参考：{'; '.join(formatted_kg['similar_cases_summary']) if formatted_kg['similar_cases_summary'] else '无相关案例'}
- 风险演化路径：{formatted_kg['causal_paths_summary']}"""

        # 添加评估标准和要求
        prompt += """

【评估标准与判断原则】
请作为资深建筑安全专家，综合分析以上所有信息（现场情况、法规依据、历史案例、风险演化路径），
运用专业知识和经验进行智能判断，确定风险等级：

1. 必须立即处理：
   - 存在直接的人员伤亡风险（如高空坠落、触电、中毒等）
   - 涉及结构安全威胁（如承重构件损坏、基础沉降、墙体开裂等）
   - 违反强制性法规条款且后果严重
   - 知识图谱显示类似案例曾导致严重事故
   - 因果路径指向严重结构问题或安全事故

2. 建议整改优化：
   - 违反建筑安全规范但不会立即造成严重后果
   - 存在安全隐患需要整改但紧急程度中等
   - 设备设施不符合标准要求
   - 历史案例显示此类问题可能演化为更严重风险

3. 提醒关注观察：
   - 潜在风险需要持续监控
   - 风险事件不显著但具有演化可能性
   - 预防性措施不足但暂无直接危险
   - 需要定期检查和维护的情况

4. 信息不完整：
   - 缺乏关键信息（时间、地点、具体描述）无法准确评估
   - 图像或文本描述过于模糊
   - 现有信息不足以支撑专业判断

【评估要求】
- 请基于专业知识进行综合分析，不要简单地按关键词匹配
- 充分考虑法规条款的强制性程度和违规后果
- 结合知识图谱中的历史案例和因果关系进行风险预判
- 重点评估：人员安全风险、结构安全风险、法规符合性、风险演化趋势
- 给出详细的推理过程和专业建议

请严格按以下JSON格式输出：
{
    "risk_level": "必须立即处理/建议整改优化/提醒关注观察/信息不完整",
    "violation": true/false,
    "law_clause": "具体法规条款或无",
    "law_reason": "法规分析说明",
    "evidence_chain": "综合所有信息的完整推理过程，包括对现场情况、法规要求、历史案例、风险路径的专业分析"
}"""

        return prompt

    def _format_image_descriptions(self, images: List[Dict[str, Any]]) -> str:
        """
        格式化图像描述信息

        Args:
            images: 图像列表

        Returns:
            str: 格式化后的图像描述字符串
        """
        if not images:
            return ""

        image_descriptions = []
        for i, image in enumerate(images):
            if isinstance(image, dict):
                desc = image.get("description")
                if desc:
                    image_descriptions.append(desc)
                else:
                    image_descriptions.append(f"图像{i+1}")
            else:
                image_descriptions.append(f"图像{i+1}")

        return "，".join(image_descriptions) if image_descriptions else ""

    def _validate_llm_assessment(self, assessment: Dict[str, Any]) -> bool:
        """
        验证LLM评估结果的有效性

        Args:
            assessment: LLM返回的评估结果

        Returns:
            bool: 评估结果是否有效
        """
        if not assessment:
            return False

        # 检查必需字段
        required_fields = ["risk_level", "violation", "evidence_chain"]
        for field in required_fields:
            if field not in assessment:
                return False

        # 检查风险等级是否有效
        valid_risk_levels = ["必须立即处理", "建议整改优化", "提醒关注观察", "信息不完整"]
        if assessment.get("risk_level") not in valid_risk_levels:
            return False

        # 检查violation字段是否为布尔值
        if not isinstance(assessment.get("violation"), bool):
            return False

        # 检查evidence_chain是否为非空字符串
        if not isinstance(assessment.get("evidence_chain"), str) or not assessment.get("evidence_chain").strip():
            return False

        return True



    def _call_llm_with_retry(self, prompt: str, max_retries: int = 3) -> Optional[Dict[str, Any]]:
        """
        LLM调用接口，用于风险评估（返回JSON格式）

        Args:
            prompt: 提示词
            max_retries: 最大重试次数

        Returns:
            Optional[Dict[str, Any]]: 解析后的LLM评估结果，失败时返回None
        """
        for attempt in range(max_retries):
            try:
                # 调用LLM
                result = self.call_llm(prompt)

                if result:
                    self.log_info(f"LLM评估结果: {result}")
                    assessment = data_service.extract_json_from_text(str(result))
                    if assessment and self._validate_llm_assessment(assessment):
                        return assessment
                    else:
                        self.log_warning(f"LLM返回的JSON格式无效，尝试重试 {attempt + 1}/{max_retries}")
                else:
                    self.log_warning(f"LLM调用无结果，尝试重试 {attempt + 1}/{max_retries}")

            except Exception as e:
                self.log_error(f"LLM调用失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")

        self.log_error(f"LLM调用失败，已达到最大重试次数 {max_retries}")
        return None



    def evaluate_hazard(self, hazard: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估单个隐患的风险等级
        
        Args:
            hazard: 隐患信息
            
        Returns:
            Dict[str, Any]: 评估结果
        """
        self.log_info(f"评估隐患: {hazard.get('hazard_id', 'unknown')}")
        
        try:
            # 检查信息完整性
            is_complete, missing_fields = self.check_hazard_completeness(hazard)
            if not is_complete:
                missing = ", ".join(missing_fields)
                self.log_info(f"信息不完整，缺失字段: {missing}")
                return self._create_default_assessment(hazard, f"信息不完整，缺失关键字段: {missing}，无法进行全面评估。")
                
            # 获取隐患描述 - 使用新的HazardItem字段
            description = hazard.get("hazard_description", "")
            if not description:
                self.log_warning("隐患描述为空")
                return self._create_default_assessment(hazard, "隐患描述为空，无法进行评估。")
            
            # 获取法规信息
            law_clause, is_mandatory, law_explanation = self.get_law_rag_info(description)
            
            # 获取知识图谱信息
            kg_context, kg_explanation, graph_path = self.get_knowledge_graph_info(description)

            # 格式化图像描述
            image_description = self._format_image_descriptions(hazard.get("images", []))

            # 构造增强版评估提示词
            prompt = self._build_enhanced_prompt(hazard, law_clause, kg_context, image_description)
            
            # 调用LLM进行评估
            self.log_info("调用LLM进行风险评估")
            assessment = self._call_llm_with_retry(prompt, max_retries=3)

            if assessment and self._validate_llm_assessment(assessment):
                # 直接使用LLM的评估结果，不再用规则覆盖
                final_risk_level = assessment.get("risk_level", "信息不完整")

                # 构建标准化的评估结果 - 按照新的RiskAssessment格式
                risk_assessment = {
                    "hazard_id": hazard.get("hazard_id", ""),
                    "weibo_id": hazard.get("weibo_id", ""),
                    "weibo_url": hazard.get("weibo_url", ""),
                    "timestamp": hazard.get("timestamp", ""),
                    "location": hazard.get("location", ""),
                    "author": hazard.get("author", ""),
                    "hazard_description": hazard.get("hazard_description", ""),
                    "risk_level": final_risk_level,
                    "violation": assessment.get("violation", False),
                    "law_clause": assessment.get("law_clause") or law_clause,
                    "graph_path": graph_path or "",
                    "law_reason": assessment.get("law_reason", ""),
                    "evidence_chain": assessment.get("evidence_chain", ""),
                    "images": hazard.get("images", []),
                    "videos": hazard.get("videos", []),
                    "report_markdown": self._generate_report_markdown(hazard, final_risk_level, assessment, graph_path, law_clause)
                }

                return risk_assessment
            else:
                self.log_warning("无法从响应中提取有效的JSON数据")
                return self._create_default_assessment(hazard, "系统无法正确解析评估结果")
                
        except Exception as e:
            self.log_error(f"评估隐患失败: {str(e)}")
            return self._create_default_assessment(hazard, f"评估过程发生错误: {str(e)}")

    def _create_default_assessment(self, hazard: Dict[str, Any], reason: str) -> Dict[str, Any]:
        """
        创建默认的评估结果

        Args:
            hazard: 隐患信息
            reason: 失败原因

        Returns:
            Dict[str, Any]: 默认评估结果，符合RiskAssessment格式
        """
        return {
            "hazard_id": hazard.get("hazard_id", ""),
            "weibo_id": hazard.get("weibo_id", ""),
            "weibo_url": hazard.get("weibo_url", ""),
            "timestamp": hazard.get("timestamp", ""),
            "location": hazard.get("location", ""),
            "author": hazard.get("author", ""),
            "hazard_description": hazard.get("hazard_description", ""),
            "risk_level": "信息不完整",
            "violation": False,
            "law_clause": "",
            "graph_path": "",
            "law_reason": "",
            "evidence_chain": reason,
            "images": hazard.get("images", []),
            "videos": hazard.get("videos", []),
            "report_markdown": f"## 隐患评估失败\n\n**隐患ID**: {hazard.get('hazard_id', '未知')}\n\n**失败原因**: {reason}"
        }

    def _generate_report_markdown(self, hazard: Dict[str, Any], risk_level: str, assessment: Dict[str, Any], graph_path: str, law_clause: str) -> str:
        """
        生成单个隐患的报告markdown段落

        Args:
            hazard: 隐患信息
            risk_level: 风险等级
            assessment: 评估结果
            graph_path: 图谱路径
            law_clause: 法规条款

        Returns:
            str: markdown格式的报告段落
        """
        # 构建完整的报告结构，确保所有字段都显示
        markdown = f"## 隐患评估报告\n\n"

        # 辅助函数处理空值
        def format_value(value):
            return value if value and str(value).strip() else 'null'

        # 基本信息 - 完整显示所有字段
        markdown += f"**隐患ID**: {format_value(hazard.get('hazard_id'))}\n\n"
        markdown += f"**风险等级**: {format_value(risk_level)}\n\n"
        markdown += f"**时间**: {format_value(hazard.get('timestamp'))}\n\n"
        markdown += f"**地点**: {format_value(hazard.get('location'))}\n\n"
        markdown += f"**来源**: {format_value(hazard.get('author'))}\n\n"
        markdown += f"**描述**: {format_value(hazard.get('hazard_description'))}\n\n"

        # 违规信息
        violation = assessment.get("violation", False)
        markdown += f"**是否违规**: {'是' if violation else '否'}\n\n"

        # 法规信息
        markdown += f"**相关法规**: {format_value(law_clause)}\n\n"
        markdown += f"**法规说明**: {format_value(assessment.get('law_reason'))}\n\n"

        # 图谱路径
        # 图谱路径 - 支持多行显示
        if graph_path and graph_path.strip():
            # 如果包含换行符，说明是多条路径
            if '\n' in graph_path:
                markdown += f"**图谱路径**:\n{graph_path}\n\n"
            else:
                markdown += f"**图谱路径**: {graph_path}\n\n"
        else:
            markdown += f"**图谱路径**: null\n\n"

        # 推理链条说明
        markdown += f"**推理链条说明**: {format_value(assessment.get('evidence_chain'))}\n\n"

        # 微博地址
        markdown += f"**微博地址**: {format_value(hazard.get('weibo_url'))}\n\n"

        return markdown
    
    @safe_execute
    def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行风险评估任务
        
        Args:
            task_input: 任务输入，包含以下字段
                - data: 隐患数据列表
                - task_id: 任务ID（可选）
                
        Returns:
            Dict[str, Any]: 任务输出，包含评估结果
        """
        self.log_info("开始执行风险评估任务")
        
        # 验证输入
        if not task_input or not isinstance(task_input, dict):
            self.log_error("任务输入无效，应为字典类型")
            
            # 生成任务ID
            task_id = data_service.generate_task_id()
            
            # 更新任务状态为失败
            data_service.update_task_status(task_id, "evaluate", "failed", {
                "error": "任务输入无效，应为字典类型",
                "time_failed": datetime.now().isoformat()
            })
            
            return {"status": "error", "error": "任务输入无效，应为字典类型", "task_id": task_id}
            
        # 获取隐患数据 - 支持data参数或input_file参数
        hazards = []

        if "data" in task_input:
            # 直接使用提供的数据
            hazards = task_input.get("data", [])
        elif "input_file" in task_input:
            # 从文件加载数据
            input_file = task_input.get("input_file")
            self.log_info(f"从文件加载隐患数据: {input_file}")
            try:
                import json
                with open(input_file, 'r', encoding='utf-8') as f:
                    hazards = json.load(f)
                self.log_info(f"成功加载 {len(hazards)} 条隐患数据")
            except Exception as e:
                self.log_error(f"加载输入文件失败: {e}")
                # 获取任务ID
                task_id = task_input.get("task_id", data_service.generate_task_id())
                # 更新任务状态为失败
                data_service.update_task_status(task_id, "evaluate", "failed", {
                    "error": f"加载输入文件失败: {e}",
                    "time_failed": datetime.now().isoformat()
                })
                return {"status": "error", "error": f"加载输入文件失败: {e}", "task_id": task_id}
        else:
            self.log_error("缺少必要参数: data 或 input_file")

            # 获取任务ID
            task_id = task_input.get("task_id", data_service.generate_task_id())

            # 更新任务状态为失败
            data_service.update_task_status(task_id, "evaluate", "failed", {
                "error": "缺少必要参数: data 或 input_file",
                "time_failed": datetime.now().isoformat()
            })

            return {"status": "error", "error": "缺少必要参数: data 或 input_file", "task_id": task_id}
        
        # 获取任务ID
        task_id = task_input.get("task_id")
        if not task_id:
            # 生成任务ID
            task_id = data_service.generate_task_id()
            
        # 确保hazards是列表类型
        if not isinstance(hazards, list):
            self.log_warning("输入数据格式错误，应为列表类型")
            
            # 更新任务状态为失败
            data_service.update_task_status(task_id, "evaluate", "failed", {
                "error": "输入数据格式错误，应为列表类型",
                "time_failed": datetime.now().isoformat()
            })
            
            return {
                "status": "error", 
                "error": "输入数据格式错误，应为列表类型", 
                "task_id": task_id
            }
        
        # 如果输入为空
        if not hazards:
            self.log_warning("输入数据为空")
            
            # 更新任务状态为完成（但没有数据）
            data_service.update_task_status(task_id, "evaluate", "completed", {
                "warning": "输入数据为空",
                "end_time": datetime.now().isoformat(),
                "processed_count": 0,
                "assessment_count": 0
            })
            
            return {
                "status": "success", 
                "data": [], 
                "task_id": task_id,
                "message": "输入数据为空",
                "processed_count": 0,
                "duration": 0
            }
            
        # 记录处理开始时间
        start_time = time.time()
            
        # 更新任务状态为运行中
        data_service.update_task_status(task_id, "evaluate", "running", {
            "start_time": datetime.now().isoformat()
        })

        # 初始化进度条
        progress_bar = create_progress_bar(
            total=len(hazards),
            stage_name="风险评估",
            logger=self.logger,
            update_threshold=5  # 每5%显示一次进度
        )

        # 评估风险
        assessments = []
        processed_count = 0
        batch_size = 5  # 每处理5个隐患执行一次内存清理
            
        for hazard in hazards:
            # 确保hazard是字典类型
            if not isinstance(hazard, dict):
                self.log_warning(f"跳过非字典类型的隐患数据: {type(hazard)}")
                continue
                
            # 评估隐患风险
            try:
                assessment = self.evaluate_hazard(hazard)
                assessments.append(assessment)
                
            except Exception as e:
                self.log_error(f"评估隐患时出错: {str(e)}")
                # 添加一个默认的评估结果
                assessments.append(self._create_default_assessment(hazard, f"评估过程出错: {str(e)}"))
            
            processed_count += 1

            # 更新进度条
            progress_bar.update(current=processed_count)
        
        # 完成进度条
        progress_bar.finish()

        # 处理时间
        process_time = time.time() - start_time

        self.log_info(f"风险评估完成，处理 {processed_count} 条隐患，耗时 {process_time:.2f} 秒")
        
        # 更新任务状态为完成
        data_service.update_task_status(task_id, "evaluate", "completed", {
            "end_time": datetime.now().isoformat(),
            "duration": f"{process_time:.2f}秒",
            "processed_count": processed_count,
            "assessment_count": len(assessments)
        })
                
        # 保存评估结果（包括空结果）
        assessment_file = self.get_path("evaluation_dir") / f"{task_id}.json"

        try:
            # 保存为JSON文件
            data_service.save_json(assessments, assessment_file)
            self.log_info(f"评估结果已保存: {assessment_file}")

            # 同时保存到任务数据中
            data_service.save_task_data(task_id, "evaluation", assessments)
            self.log_info(f"评估结果已保存到任务: {task_id}")

            if not assessments:
                self.log_warning("未生成任何评估结果，但已保存空结果")
        except Exception as e:
            self.log_error(f"保存评估结果失败: {str(e)}")
            raise TaskException(f"保存评估结果失败: {str(e)}", task_id=task_id)
        
        return {
            "status": "success",
            "data": assessments,
            "output_file": assessment_file,
            "evaluation_data_path": str(assessment_file) if assessments else None,
            "task_id": task_id,
            "processed_count": processed_count,
            "duration": process_time
        }
    

    def __str__(self) -> str:
        """返回智能体描述"""
        return f"EvaluatorAgent(name='{self.name}', role='{self.role}')" 

    def check_hazard_completeness(self, hazard: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        检查隐患信息是否完整 - 适配新的HazardItem格式

        Args:
            hazard: 隐患信息

        Returns:
            Tuple[bool, List[str]]: (是否完整, 缺失字段列表)
        """
        # 新的HazardItem格式的必需字段
        required_fields = ["timestamp", "location", "hazard_description"]
        missing = []

        for field in required_fields:
            if field not in hazard or not hazard[field] or (isinstance(hazard[field], str) and hazard[field].strip() == ""):
                missing.append(field)

        return len(missing) == 0, missing

    @property
    def law_rag_engine(self):
        global _law_rag_engine
        return _law_rag_engine
    
    @property
    def search_law_tool(self):
        return self._create_search_law_tool()
