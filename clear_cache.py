#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目缓存清理脚本
清理Python缓存、日志文件、数据缓存等
"""

import os
import shutil
import sys
from pathlib import Path
from typing import List, Set


class CacheCleaner:
    """缓存清理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.deleted_files: List[str] = []
        self.deleted_dirs: List[str] = []
        self.errors: List[str] = []
        
    def clean_python_cache(self) -> None:
        """清理Python缓存文件"""
        print("🧹 清理Python缓存文件...")
        
        # 查找所有__pycache__目录
        for pycache_dir in self.project_root.rglob("__pycache__"):
            try:
                shutil.rmtree(pycache_dir)
                self.deleted_dirs.append(str(pycache_dir))
                print(f"  ✅ 删除目录: {pycache_dir}")
            except Exception as e:
                error_msg = f"删除__pycache__目录失败: {pycache_dir} - {e}"
                self.errors.append(error_msg)
                print(f"  ❌ {error_msg}")
        
        # 查找所有.pyc文件
        for pyc_file in self.project_root.rglob("*.pyc"):
            try:
                pyc_file.unlink()
                self.deleted_files.append(str(pyc_file))
                print(f"  ✅ 删除文件: {pyc_file}")
            except Exception as e:
                error_msg = f"删除.pyc文件失败: {pyc_file} - {e}"
                self.errors.append(error_msg)
                print(f"  ❌ {error_msg}")
    
    def clean_log_files(self) -> None:
        """清理日志文件"""
        print("📝 清理日志文件...")
        
        # 清理系统日志
        log_patterns = ["*.log", "*.log.*"]
        for pattern in log_patterns:
            for log_file in self.project_root.rglob(pattern):
                try:
                    log_file.unlink()
                    self.deleted_files.append(str(log_file))
                    print(f"  ✅ 删除日志: {log_file}")
                except Exception as e:
                    error_msg = f"删除日志文件失败: {log_file} - {e}"
                    self.errors.append(error_msg)
                    print(f"  ❌ {error_msg}")
    
    def clean_data_cache(self) -> None:
        """清理数据缓存"""
        print("💾 清理数据缓存...")
        
        # 清理data目录下的缓存
        data_dirs = [
            "data/raw",
            "data/structured",
            "data/evaluation",
            "data/reports"
        ]
        
        for data_dir in data_dirs:
            dir_path = self.project_root / data_dir
            if dir_path.exists():
                try:
                    # 删除目录内容但保留目录结构
                    for item in dir_path.iterdir():
                        if item.is_file():
                            item.unlink()
                            self.deleted_files.append(str(item))
                            print(f"  ✅ 删除数据文件: {item}")
                        elif item.is_dir():
                            shutil.rmtree(item)
                            self.deleted_dirs.append(str(item))
                            print(f"  ✅ 删除数据目录: {item}")
                except Exception as e:
                    error_msg = f"清理数据目录失败: {dir_path} - {e}"
                    self.errors.append(error_msg)
                    print(f"  ❌ {error_msg}")
    
    def clean_status_files(self) -> None:
        """清理状态文件"""
        print("📊 清理状态文件...")
        
        status_dir = self.project_root / "logs/status"
        if status_dir.exists():
            try:
                for status_file in status_dir.glob("*.json"):
                    status_file.unlink()
                    self.deleted_files.append(str(status_file))
                    print(f"  ✅ 删除状态文件: {status_file}")
            except Exception as e:
                error_msg = f"清理状态文件失败: {status_dir} - {e}"
                self.errors.append(error_msg)
                print(f"  ❌ {error_msg}")
    
    def clean_temp_files(self) -> None:
        """清理临时文件"""
        print("🗑️ 清理临时文件...")
        
        # 常见的临时文件模式
        temp_patterns = [
            "*.tmp", "*.temp", "*~", ".DS_Store", "Thumbs.db",
            "*.bak", "*.swp", "*.swo"
        ]
        
        for pattern in temp_patterns:
            for temp_file in self.project_root.rglob(pattern):
                try:
                    temp_file.unlink()
                    self.deleted_files.append(str(temp_file))
                    print(f"  ✅ 删除临时文件: {temp_file}")
                except Exception as e:
                    error_msg = f"删除临时文件失败: {temp_file} - {e}"
                    self.errors.append(error_msg)
                    print(f"  ❌ {error_msg}")
    
    def clean_scrapy_cache(self) -> None:
        """清理Scrapy缓存"""
        print("🕷️ 清理Scrapy缓存...")
        
        # 查找.scrapy目录
        for scrapy_dir in self.project_root.rglob(".scrapy"):
            try:
                shutil.rmtree(scrapy_dir)
                self.deleted_dirs.append(str(scrapy_dir))
                print(f"  ✅ 删除Scrapy缓存: {scrapy_dir}")
            except Exception as e:
                error_msg = f"删除Scrapy缓存失败: {scrapy_dir} - {e}"
                self.errors.append(error_msg)
                print(f"  ❌ {error_msg}")
    
    def print_summary(self) -> None:
        """打印清理摘要"""
        print("\n" + "="*60)
        print("🎉 缓存清理完成!")
        print("="*60)
        
        print(f"📁 删除的目录数量: {len(self.deleted_dirs)}")
        print(f"📄 删除的文件数量: {len(self.deleted_files)}")
        print(f"❌ 错误数量: {len(self.errors)}")
        
        if self.errors:
            print("\n⚠️ 清理过程中的错误:")
            for error in self.errors:
                print(f"  • {error}")
        
        # 计算释放的空间（简单估算）
        total_items = len(self.deleted_files) + len(self.deleted_dirs)
        print(f"\n✨ 总共清理了 {total_items} 个项目")
    
    def clean_all(self) -> None:
        """执行完整的缓存清理"""
        print("🚀 开始清理项目缓存...")
        print(f"📂 项目根目录: {self.project_root}")
        print("-" * 60)
        
        # 执行各种清理操作
        self.clean_python_cache()
        self.clean_log_files()
        self.clean_data_cache()
        self.clean_status_files()
        self.clean_temp_files()
        self.clean_scrapy_cache()
        
        # 打印摘要
        self.print_summary()


def main():
    """主函数"""
    # 获取项目根目录
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = "."
    
    # 确认清理操作
    print("⚠️ 即将清理项目缓存，包括:")
    print("  • Python缓存文件 (__pycache__, *.pyc)")
    print("  • 日志文件 (*.log)")
    print("  • 数据缓存 (data目录)")
    print("  • 状态文件 (status目录)")
    print("  • 临时文件")
    print("  • Scrapy缓存")
    
    response = input("\n是否继续? (y/N): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("❌ 取消清理操作")
        return
    
    # 执行清理
    cleaner = CacheCleaner(project_root)
    cleaner.clean_all()


if __name__ == "__main__":
    main()
